import { Msg } from "@/api/chat";
import { URLParams } from "./tools";
const userAgent = window.navigator.userAgent.toLowerCase();
export const isJSZP =
  !URLParams.get("bizCode") ||
  /*教师招聘*/ URLParams.get("bizCode") === "10001";
export const isSales =
  /*企微客服销售*/ URLParams.get("bizCode") === "10002" ||
  /*线上测试回归*/ URLParams.get("bizCode") === "10099" ||
  /*im私教销售*/ URLParams.get("bizCode") === "10007" ||
  /*企微私教销售*/ URLParams.get("bizCode") === "10012";

export const isParrotSales =
  /*私教销售*/ URLParams.get("bizCode") === "10007" ||
  /*企微私教销售*/ URLParams.get("bizCode") === "10012";

export const isDDZ =
  /*驾考私教督导组*/ URLParams.get("bizCode") === "10009" ||
  /*驾考私教官方讲师组*/ URLParams.get("bizCode") === "10010";

export const isJXBM = /*驾校报名*/ URLParams.get("bizCode") === "10011";
export const isCarKSsell =
  /*小车科四私教售卖*/ URLParams.get("bizCode") === "10013";
export const isIOS = userAgent.indexOf("iphone") > -1;

export const isGroupMemberMsg = (msg: Msg) => {
  return [200, 201].includes(msg.msgType);
};

export const isSystemMsg = (msg: Msg) => {
  return [12, 91, 92, 99, 200, 201, 202].includes(msg.msgType);
};

export const getMsgHint = (msg: Msg) => {
  let newMsg: string;
  const sender = msg.staff || msg.customer! || {};

  if (msg.isRevoke) {
    return `"${sender.nickname}" 撤回了一条消息`;
  }

  switch (msg.msgType) {
    case 11:
    case 10006:
      newMsg = msg.msgContent.text;
      break;
    case 12:
      newMsg = msg.msgContent.headText;
      break;
    case 13:
      newMsg = `[图片]`;
      break;
    case 14:
      newMsg = `[语音]`;
      break;
    case 15:
      newMsg = `[视频]`;
      break;
    case 16:
      newMsg = `[文件] ${msg.msgContent.name || "未知文件"}`;
      break;
    case 91:
      newMsg = `"${sender.nickname}" 进入了房间`;
      break;
    case 92:
      newMsg = "![发送失败]";
      break;
    case 93:
      newMsg = `"${sender.nickname}" 撤回了一条消息`;
      break;
    case 99:
      newMsg = `[课堂消息]`;
      break;
    case 200:
      newMsg = `"${msg.msgContent?.NewMemberList?.map((item) => item.nickName).join("、")}" 加入群聊`;
      break;
    case 201:
      newMsg = `"${msg.msgContent?.ExitMemberList?.map((item) => item.nickName).join("、")}" 退出群聊`;
      break;
    case 202:
      newMsg = `"${msg.account.name}" 修改了群公告`;
      break;
    case 203:
      newMsg = `[群聊已解散]`;
      break;
    case 111:
      newMsg = `[图片] ${msg.msgContent.name}`;
      break;
    case 112:
      newMsg = `[文件] ${msg.msgContent.name || "未知文件"}`;
      break;
    case 113:
      newMsg = `[视频] ${msg.msgContent.name}`;
      break;
    case 114:
      newMsg = `[小程序链接] ${msg.msgContent.title}`;
      break;
    case 115:
      newMsg = `[图文链接] ${msg.msgContent.title}`;
      break;
    case 116:
      newMsg = `[${msg.msgContent.name}]`;
      break;
    case 117:
      newMsg = `[${msg.msgContent.name}]`;
      break;
    case 119:
      newMsg = `[学情分析链接] ${msg.msgContent.title}`;
      break;
    case 10000:
      newMsg = `[报价单消息] ${msg.msgContent.msgText}`;
      break;
    case 10001:
      newMsg = `[快捷指令] ${msg.msgContent.text}`;
      break;
    case 10002:
      newMsg = `[小程序消息] ${msg.msgContent.title}`;
      break;
    case 10003:
      newMsg = `[私教学员信息收集表单消息] ${msg.msgContent.title}`;
      break;
    case 10004:
      newMsg = `[学情分析消息] ${msg.msgContent.title}`;
      break;
    case 10005:
      newMsg = `[支付成功] ${msg.msgContent.title}`;
      break;
    case 10007:
      newMsg = `[位置] ${msg.msgContent.name}`;
      break;
    case 10008:
      newMsg = `[通用卡片] ${msg.msgContent.text}`;
      break;
    case 10009:
      newMsg = `[优惠券卡片] ${msg.msgContent.couponTitle}`;
      break;
    case 10010:
      newMsg = `[表格卡片] ${msg.msgContent.recommendText}`;
      break;
    case 10011:
      newMsg = `[图文链接卡片] ${msg.msgContent.title}`;
      break;
    case 10012:
      newMsg = `[驾校报名卡片] ${msg.msgContent.className}`;
      break;
    case 10020:
      newMsg = `[图文链接卡片-上下布局] ${msg.msgContent.title}`;
      break;
    case 10021:
      newMsg = `[驾校线上成交-拼团消息] ${msg.msgContent.className}`;
      break;
    default:
      newMsg = "[未知消息]";
      break;
  }
  return newMsg;
};

export enum MsgTypeMapper {
  "text" = 11, // "文本",
  "menu" = 12, // "菜单",
  "image" = 13, // "图片",
  "voice" = 14, // "语音",
  "video" = 15, // "视频",
  "file" = 16, // "文件",
  "mini_program" = 17, // "小程序消息",
  "event_enter" = 91, // "事件:进入",
  "event_msg_send_fail" = 92, // "事件:消息发送失败",
  "event_user_recall_msg" = 93, // "事件:撤回消息",
  "event_common_hidden" = 99, // "事件:通用事件(对客户隐藏)",
  "command_image" = 111, // "指令:图片",
  "command_file" = 112, // "指令:文件",
  "command_video" = 113, // "指令:视频",
  "command_mini_program" = 114, // "指令:小程序消息",
  "command_link" = 115, // "指令:图文链接消息",
  //个人名片指令，其实发送的是图片。在发送消息的时候，忽略字段content的内容，会将消息替换成发送对应的人的微信二维码图片
  "command_staff_card" = 116, // "指令:个人名片",
  //个人群名片指令，其实发送的是图片。在发送消息的时候，忽略字段content的内容，会将消息替换成发送对应的人的微信二维码图片
  "command_staff_group_card" = 117, // "指令:个人群名片",
  //二维码指令
  "command_qr_code" = 118, // "指令:发送二维码",
  //学情分析指令
  "command_learn_situation" = 119, // "指令:发送学情分析",
  "im_group_join" = 200, // "im群聊进入",
  "im_group_exit" = 201, // "im群聊退出",
  "im_group_changed" = 202, // "im群资料修改",
  "priceList" = 10000, // "报价单",
  "shortcutCommand" = 10001, // "快捷指令",
  "miniProgram" = 10002, // "小程序链接",
  "collectData" = 10003, // "信息收集",
  "learnSituation" = 10004, // "学情分析",
  "paySuccess" = 10005, // "订单支付成功",
  "face" = 10006, // "典典表情包",
  "location" = 10007, // "定位消息"
  "commonCard" = 10008, // "通用卡片消息"
  "coupon" = 10009, // "优惠券卡片"
  "commonTable" = 10010, // "表格卡片"
  "imgTextLink" = 10011, // "图文链接卡片"
  "schoolSignUp" = 10012, // "驾校报名卡片"
  "imgTextLinkV2" = 10020, // "图文链接卡片2"
  "onlineDealMakeUpGroup" = 10021, // "驾校线上成交-拼团消息"
}

export const CarTypeMapper: Record<string, string> = {
  car: "小车",
  truck: "货车",
  bus: "客车",
  moto: "摩托车",
};

export const instructionsType = {
  111: "图片",
  112: "文件",
  113: "视频",
  114: "小程序",
  115: "图文链接",
  116: "个人名片",
  117: "群名片",
  10003: "学员信息表单",
  119: "学情分析",
  11: "文本",
};
