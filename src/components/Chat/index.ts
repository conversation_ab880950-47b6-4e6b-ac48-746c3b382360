import { Msg, sendMsg, MsgAction, revokeMsg, GroupMember } from "@/api/chat";
import { makeToast } from "@/utils/dom";
import { MsgTypeMapper, isParrotSales } from "@/utils/helpers";
import { ref, reactive, watch, nextTick, inject, h, VNode } from "vue";
import AtContainer from "./AtContainer.vue";
import { EventEmitter, useEventEmitter } from "@/utils/eventEmitter";
import AIComp from "@/components/ChatQuick/AI.vue";
import QAComp from "@/components/ChatQuick/QA.vue";
import Instructions from "@/components/ChatQuick/Instructions.vue";
import CustomerDetail from "@/components/ChatQuick/CustomerDetail.vue";
import MemberList from "../ChatQuick/MemberList.vue";
import JXBM from "@/components/ChatQuick/JXBM.vue";
import AIScript from "../ChatQuick/AIScript.vue";
import WordLibrary from "../ChatQuick/WordLibrary.vue";
import UserOrders from "../ChatQuick/UserOrders.vue";
import IntentionForm from "../ChatQuick/IntentionForm.vue";
import LabelList from "../ChatQuick/LabelList.vue";
import { useSessionStore, useAccountStore, useParamStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
export interface Tab {
  id: number;
  label: string;
  needCache?: boolean;
  go?: (param?: any) => void;
  paramEmitter?: () => void;
  onEmitterFn?: () => void;
  render(): VNode;
}
export function useMsgSender(appendNewMsgAfterSend: (newMsg: Msg) => void) {
  const { session } = storeToRefs(useSessionStore());
  const { account } = storeToRefs(useAccountStore());
  const { setParam } = useParamStore();
  const router = useRouter();
  const { setSingleSession } = useSessionStore();
  const flyMsg = ref("");
  const inputRef = ref<HTMLTextAreaElement>();
  const inputHeight = ref("25px");
  const justifyInputHeight = () => {
    inputHeight.value = "auto"; // 重置高度
    nextTick(() => {
      inputHeight.value = inputRef.value!.scrollHeight + "px"; // 根据内容调整高度
    });
  };
  watch(() => flyMsg.value, justifyInputHeight);
  const sending = ref(false);
  const referenceMsg = reactive({
    id: "",
    content: "",
    selected: false,
    pickedMsgContent: null as any,
  });
  const replyMsg = ref<Msg>();
  const refMsg = ref<Msg>();

  const atContainerRef = ref<typeof AtContainer>();

  const emitter = inject<EventEmitter>("emitter")!;

  const activeTabRef = ref<number>();

  const sendOtherMessage = async (getMessage: Promise<MsgAction>) => {
    if (!account.value || sending.value) return;
    sending.value = true;
    const msg = await getMessage;
    await sendMessageDirect(msg).finally(() => {
      sending.value = false;
    });
  };

  const sendMessage = async () => {
    if (!flyMsg.value || !account.value || sending.value) return;
    sending.value = true;
    await sendMessageDirect({
      msgType: 11,
      msgContent: {
        ...referenceMsg.pickedMsgContent,
        ...atContainerRef.value!.getMsgContent(),
        replyMsg: replyMsg.value && {
          data: {
            type: MsgTypeMapper[replyMsg.value.msgType],
            content: replyMsg.value.msgContent,
          },
          user: {
            avatar: replyMsg.value.staff
              ? replyMsg.value.staff.avatar
              : replyMsg.value.customer?.avatar,
            nickName: replyMsg.value.staff
              ? replyMsg.value.staff.nickname
              : replyMsg.value.customer?.nickname,
          },
        },
        refMsg: refMsg.value && {
          data: {
            type: MsgTypeMapper[refMsg.value.msgType],
            content: refMsg.value.msgContent,
          },
          user: {
            avatar: refMsg.value.staff
              ? refMsg.value.staff.avatar
              : refMsg.value.customer?.avatar,
            nickName: refMsg.value.staff
              ? refMsg.value.staff.nickname
              : refMsg.value.customer?.nickname,
          },
        },
      },
      ...(referenceMsg.selected
        ? {
            referenceMsgId: referenceMsg.id,
          }
        : {}),
    }).finally(() => {
      sending.value = false;
    });
    nextTick(() => {
      flyMsg.value = "";
      replyMsg.value = undefined;
      referenceMsg.selected = false;
      referenceMsg.pickedMsgContent = null;
      refMsg.value = undefined;
    });
  };

  const onPickMsg = (
    msg: MsgAction,
    callback?: Function,
    isSend: boolean = false,
  ) => {
    if (msg.msgType === 11 && !isSend) {
      // 如果点击了引用但消息id不一致，则重置replyMsg
      if (replyMsg.value && replyMsg.value.id !== referenceMsg.id) {
        replyMsg.value = undefined;
      }
      referenceMsg.selected = true;
      referenceMsg.pickedMsgContent = msg.msgContent;
      setTimeout(() => {
        flyMsg.value = msg.msgContent.text;
        inputRef.value!.focus();
        nextTick(() => {
          const scrollHeight = inputRef.value!.scrollHeight;
          const clientHeight = inputRef.value!.clientHeight;
          inputRef.value!.scrollTop = scrollHeight - clientHeight;
        });
      }, 100);
    } else {
      if (sending.value) {
        return;
      }
      sending.value = true;
      sendMessageDirect({
        ...msg,
        referenceMsgId: referenceMsg.id,
      })
        .then(() => {
          makeToast("发送成功", undefined, 1000);
        })
        .finally(() => {
          sending.value = false;
        });
    }
    callback && callback();
  };
  const sendMessageDirect = async (
    data: MsgAction & {
      referenceMsgId?: string;
    },
  ) => {
    const newMsg = await sendMsg({
      sessionKey: session.value!.sessionKey,
      accountId: account.value!.id,
      ...data,
    });

    appendNewMsgAfterSend(newMsg);
  };

  const cancelAISuggestion = () => {
    referenceMsg.selected = false;
    referenceMsg.pickedMsgContent = null;
  };
  const tabsMapper: Record<string, Tab> = {
    AI: {
      id: 1,
      label: "AI回复",
      needCache: true,
      go: () => {
        setParam(referenceMsg);
        router.push({
          name: "AI",
        });
      },
      render() {
        return h(AIComp);
      },
    },
    AIScript: {
      id: 2,
      label: "AI快捷回复",
      needCache: true,
      go: (msg: any) => {
        setParam(msg);
        router.push({
          name: "AIScript",
        });
      },
      render() {
        return h(AIScript);
      },
    },
    WordLibrary: {
      id: 3,
      label: "话术库",
      needCache: true,
      go: () => {
        router.push({
          name: "WordLibrary",
        });
      },
      render() {
        return h(WordLibrary);
      },
    },
    Instructions: {
      id: 4,
      label: "快捷指令",
      go: () => {
        router.push({
          name: "Instructions",
        });
      },
      render() {
        return h(Instructions);
      },
    },
    QA: {
      id: 5,
      label: "常见问题回复",
      go: () => {
        router.push({
          name: "QA",
        });
      },
      render() {
        return h(QAComp);
      },
    },
    CustomerDetailUserInfo: {
      id: 6,
      label: "用户画像",
      go: () => {
        router.push({
          name: "CustomerDetail",
          params: {
            type: "userinfo",
          },
          query: {
            title: "用户画像",
          },
        });
      },
      render() {
        return h(CustomerDetail, {
          type: "userinfo",
        });
      },
    },
    CustomerDetailOrder: {
      id: 7,
      label: "私教订单",
      go: () => {
        router.push({
          name: "CustomerDetail",
          params: {
            type: "order",
          },
          query: {
            title: "私教订单",
          },
        });
      },
      render() {
        return h(CustomerDetail, {
          type: "order",
        });
      },
    },
    CustomerDetailStudentAnalysis: {
      id: 8,
      label: "学情分析",
      go: () => {
        router.push({
          name: "CustomerDetail",
          params: {
            type: "student-analysis",
          },
          query: {
            title: "学情分析",
          },
        });
      },
      render() {
        return h(CustomerDetail, {
          type: "student-analysis",
        });
      },
    },
    CustomerDetailTeach: {
      id: 9,
      label: "报价单",
      go: () => {
        router.push({
          name: "CustomerDetail",
          params: {
            type: "teach",
          },
          query: {
            title: "报价单",
          },
        });
      },
      render() {
        return h(CustomerDetail, {
          type: "teach",
        });
      },
    },
    JXBM: {
      id: 11,
      label: "用户画像",
      go: () => {
        router.push({
          name: "JXBM",
        });
      },
      render() {
        return h(JXBM);
      },
    },
    MemberList: {
      id: 12,
      label: "群设置",
      go: () => {
        router.push({
          name: "MemberList",
        });
      },
      render() {
        return h(MemberList, {
          onSelectCustomer: (member: GroupMember) => {
            setSingleSession("customerMucangId", member.mucangId);
            setSingleSession("customerWxUnionId", "1");
            setSingleSession("staffSsoId", "1");
            setSingleSession("customerCode", "1");
            activeTabRef.value = 10;
          },
        });
      },
    },
    ChatQuick: {
      id: 13,
      label: "聊天回复",
      needCache: true,
      render() {
        return h(
          "div",
          {
            style: {
              backgroundColor: "#E6F2FB",
              borderRadius: "12px",
            },
          },
          [
            // 快捷指令
            h(Instructions, {
              onPickMsgAction: onPickMsg,
            }),
            // 常见问题回复
            h(QAComp, {
              onPickMsgAction: onPickMsg,
            }),
          ],
        );
      },
    },
    AIChat: {
      id: 14,
      label: "聊天回复",
      needCache: true,
      render() {
        return h(
          "div",
          {
            style: {
              backgroundColor: "#E6F2FB",
              borderRadius: "12px",
            },
          },
          [
            // 快捷指令
            h(Instructions, {
              onPickMsgAction: onPickMsg,
            }),

            // 常见问题回复
            h(QAComp, {
              onPickMsgAction: onPickMsg,
            }),
          ],
        );
      },
    },
    UserOrders: {
      id: 15,
      label: "用户订单",
      go: () => {
        router.push({
          name: "UserOrder",
        });
      },
      render() {
        return h(UserOrders);
      },
    },
    LabelList: {
      id: 16,
      label: "标签列表",
      needCache: true,
      go: () => {
        router.push({
          name: "LabelList",
        });
      },
      render() {
        return h(LabelList);
      },
    },
    IntentionForm: {
      id: 17,
      label: "意向表单",
      go: () => {
        router.push({
          name: "IntentionForm",
        });
      },
      render() {
        return h(IntentionForm);
      },
    },
  };

  watch(
    () => session.value!,
    () => {
      Object.assign(referenceMsg, {
        id: "",
        content: "",
        selected: false,
        pickedMsgContent: null,
      });
      replyMsg.value = undefined;
      refMsg.value = undefined;
    },
  );
  // 用于tab的事件接收
  useEventEmitter(emitter, "onPickMsgAction", (msg, isSend = false) => {
    onPickMsg(msg, undefined, isSend);
  });

  return {
    flyMsg,
    inputRef,
    inputHeight,
    sending,
    referenceMsg,
    replyMsg,
    refMsg,
    atContainerRef,
    activeTabRef,
    tabsMapper,
    cancelAISuggestion,
    sendMessage,
    sendOtherMessage,
    onPickMsg,
    async onRevokeMsg(msg: Msg) {
      await revokeMsg({ msgId: msg.id });
      msg.isRevoke = true;
    },
    onReplyMsg(msg?: Msg) {
      if (!msg) {
        // 取消回复
        replyMsg.value = undefined;
        cancelAISuggestion();
        return;
      }
      replyMsg.value = msg;
      inputRef.value!.focus();
      // 如果点击了回复，则同时更新referenceMsg
      if (replyMsg.value.id !== referenceMsg.id) {
        referenceMsg.id = msg.id;
        referenceMsg.content =
          msg.msgType === 11 || msg.msgType === 10006
            ? msg.msgContent.text
            : "";
        cancelAISuggestion();
        flyMsg.value = "";
      }
    },

    selectReferenceMsg(msg?: Msg, enutterCallback?: () => void) {
      if (isParrotSales) {
        enutterCallback && enutterCallback();
        return;
      }

      if (!msg) {
        referenceMsg.id = "";
        referenceMsg.content = "";
        return;
      }
      referenceMsg.id = msg.id;
      referenceMsg.content =
        msg.msgType === 11 || msg.msgType === 10006 ? msg.msgContent.text : "";
    },
    // 三个点中的引用按钮点击
    onrefMsg(msg?: Msg) {
      if (!msg) {
        // 取消回复
        refMsg.value = undefined;
        return;
      }
      replyMsg.value = undefined;
      referenceMsg.pickedMsgContent = "";
      inputRef.value!.focus();
      refMsg.value = msg;
    },
  };
}
