<template>
  <div class="flex-1 flex flex-col relative">
    <MsgList
      ref="msgListComp"
      :sse="sse"
      @select-reference-msg="onSelectReferenceMsg"
      @reply-msg="onReplyMsg"
      @revoke-msg="onRevokeMsg"
      @ref-msg="onrefMsg"
      @select-customer="
        (customer) => {
          setSingleSession('customerCode', customer.code);
          setSingleSession('customerMucangId', customer.mucangId);
          setSingleSession('customerWxUnionId', customer.wxUnionId);
          setSingleSession('staffSsoId', '1');
          activeTabRef = 10;
        }
      "
    />

    <div
      class="px-26 py-15 bg-[#eff7fc] text-14/19 text-[#888888]"
      v-if="
        replyMsg || (referenceMsg.selected && referenceMsg.content) || refMsg
      "
    >
      <!-- 回复 -->
      <div class="max-w-full flex items-center" v-if="replyMsg">
        <div class="flex-1 flex">
          <div
            class="w-1 border-1 border-solid border-[#c8c8c8] mr-2 flex-1"
          ></div>
          <div class="w-full">
            <span class="text-blue-500">{{ replyMsg.customer?.nickname }}</span
            >：{{ getMsgHint(replyMsg) }}
          </div>
        </div>
        <i
          class="w-21 h-21 flex-[1_auto_auto] cursor-pointer [background-size:100%_100%] bg-[url(./assets/ic_close.png)]"
          @click="onReplyMsg()"
        ></i>
      </div>
      <!-- 快捷 -->
      <div
        class="max-w-full flex items-center"
        v-else-if="referenceMsg.selected && referenceMsg.content"
      >
        <div class="flex-1 flex">
          <div class="w-1 border-1 border-solid border-[#c8c8c8] mr-2"></div>
          <div class="whitespace-normal">{{ referenceMsg.content }}</div>
        </div>
        <div
          class="flex-[1_auto_auto] w-21 h-21 cursor-pointer [background-size:100%_100%] bg-[url(./assets/ic_close.png)]"
          @click="cancelAISuggestion"
        ></div>
      </div>
      <!-- 引用 -->
      <div class="max-w-full flex items-center" v-else-if="refMsg">
        <div class="flex-1 flex">
          <div class="w-1 border-1 border-solid border-[#c8c8c8] mr-2"></div>
          <div class="whitespace-normal">{{ getMsgHint(refMsg) }}</div>
        </div>
        <div
          class="flex-[1_auto_auto] w-21 h-21 cursor-pointer [background-size:100%_100%] bg-[url(./assets/ic_close.png)]"
          @click="onrefMsg()"
        ></div>
      </div>
    </div>
    <!-- 输入区域 -->
    <div
      class="border-t-[#DADEDF] border-t-2 px-26 py-14 items-start relative"
      v-if="!session!.isDismissed"
    >
      <!-- 输入区域顶部 -->
      <div class="flex justify-between">
        <IconButton
          @send-message="sendOtherMessage"
          @insert-emoji="
            (emoji) => atContainerRef!.insertEmoji(emoji.text, emoji.imgUrl)
          "
        ></IconButton>
        <AccountSelect />
      </div>
      <label class="self-center w-full mt-14 block">
        <AtContainer
          ref="atContainerRef"
          :input-ref="inputRef!"
          v-model="flyMsg"
        >
          <textarea
            ref="inputRef"
            class="text-16/22 outline-none border-none resize-none min-h-100 max-h-220 mb-40 w-full bg-transparent"
            :readonly="sending"
            :style="{ height: inputHeight }"
            v-model="flyMsg"
            @keydown.enter.exact.prevent="sendMessage"
            @keydown="handleKeyShortCut"
            rows="1"
            maxlength="1000"
            placeholder="按Shift+Enter键换行"
            autofocus
          ></textarea>
        </AtContainer>
      </label>
      <!-- 输入区域发送按钮 -->
      <div
        class="self-end flex items-center justify-center rounded-10 w-107 h-39 text-15 transition-all duration-600 ease-in-out absolute right-26 bottom-14"
        :class="
          flyMsg && !sending
            ? 'bg-[#04a5ff] text-white cursor-pointer'
            : 'bg-[#04a5ff] text-white opacity-50 cursor-not-allowed'
        "
        @click="sendMessage"
      >
        发送{{ sending ? "中..." : "" }}
      </div>
    </div>
    <div
      class="flex items-center justify-center text-gray-500 text-16 h-60"
      v-else
    >
      群聊已解散
    </div>
  </div>
  <!-- 右侧 -->
  <div class="w-320 flex flex-col mr-10 mb-9" v-if="!session!.isDismissed">
    <div
      class="flex justify-center text-center px-7 my-9 bg-[#F8FCFF] rounded-14 text-13/20 text-[#747474]"
    >
      <div
        class="cursor-pointer flex items-center justify-center px-8 py-10"
        :class="{ 'text-[#333] font-bold activeTab': activeTabRef === tab.id }"
        @click="activeTabRef = tab.id"
        v-for="tab in tabsList"
        :key="tab.id"
      >
        {{ tab.label }}
      </div>
    </div>
    <div class="h-0 flex-1 overflow-hidden rounded-14 bg-[#F2FAFF] p-5">
      <div class="h-full overflow-y-scroll relative">
        <template v-for="tab in tabsList" :key="tab.id">
          <component
            :is="tab"
            v-if="activeTabRef === tab.id || tabsCache[tab.id]"
            v-show="activeTabRef === tab.id"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CustomerDetail from "@/components/ChatQuick/CustomerDetail.vue";
import JXBM from "@/components/ChatQuick/JXBM.vue";
import IconButton from "../Common/IconButton/Win.vue";
import AccountSelect from "../Common/AccountSelect/Win.vue";
import MsgList from "./MsgList.vue";
import { ref, reactive, computed, watch, h, inject } from "vue";
import { Msg } from "@/api/chat";
import { EventEmitter } from "@/utils/eventEmitter";
import { isJXBM } from "@/utils/helpers";
import { useMsgSender, type Tab } from "./index";
import { getMsgHint } from "@/utils/helpers";
import AtContainer from "./AtContainer.vue";
import { URLParams } from "@/utils/tools";
import { useSessionStore, useAccountStore } from "@/store/index";
import { storeToRefs } from "pinia";

defineProps<{
  sse?: EventEmitter;
}>();
const emit = defineEmits(["removeCurrent", "updateGroup"]);

const { session } = storeToRefs(useSessionStore());
const { account } = storeToRefs(useAccountStore());
const { setSingleSession } = useSessionStore();
const msgListComp = ref<typeof MsgList>();
const emitter = inject<EventEmitter>("emitter")!;

const {
  flyMsg,
  inputRef,
  inputHeight,
  sending,
  referenceMsg,
  replyMsg,
  atContainerRef,
  refMsg,
  tabsMapper,
  activeTabRef,
  cancelAISuggestion,
  sendMessage,
  sendOtherMessage,
  onReplyMsg,
  onRevokeMsg,
  onrefMsg,
  selectReferenceMsg,
} = useMsgSender((msg) => {
  msgListComp.value!.appendNewMsgAfterSend(msg);
});

const tabsList = computed<Tab[]>(() => {
  const allSceneMap: Record<string, Array<keyof typeof tabsMapper>> = {
    "10001": ["AIChat"],
    "10002": [
      "AIChat",
      "CustomerDetailUserInfo",
      "CustomerDetailOrder",
      "CustomerDetailStudentAnalysis",
      "CustomerDetailTeach",
    ],
    "10006": ["AIChat"],
    "10007": [
      "AIScript",
      "CustomerDetailUserInfo",
      "ChatQuick",
      "CustomerDetailStudentAnalysis",
      "CustomerDetailTeach",
      "LabelList",
    ],
    "10009": ["AIChat", "CustomerDetailUserInfo"],
    "10010": ["AIChat", "CustomerDetailUserInfo"],
    "10011": ["AIChat", "JXBM", "UserOrders", "IntentionForm"],
  };
  allSceneMap["10013"] =
    allSceneMap["10099"] =
    allSceneMap["10012"] =
      allSceneMap["10007"];
  const tabs = allSceneMap[URLParams.get("bizCode") || "10001"] || ["AIChat"];

  if (session.value!.isGroup) {
    const userOrder = (isJXBM && tabsMapper["UserOrders"]) as Tab;
    const tabArr = [
      tabsMapper[tabs[0]],
      tabsMapper["MemberList"],
      {
        id: 10,
        label: "学员档案",
        render() {
          if (!isJXBM) {
            return h(CustomerDetail, {
              type: "userinfo",
              session: session.value!,
              account: account.value!,
            });
          } else {
            return h(JXBM, {
              session: session.value!,
            });
          }
        },
      },
    ];
    userOrder && tabArr.push(userOrder);
    return tabArr;
  } else {
    return tabs.map((tab) => {
      return tabsMapper[tab];
    });
  }
});
activeTabRef.value = tabsList.value[0].id;

/** 有些tab跟会话无关，应该常驻，懒加载后做一个缓存 */
const tabsCache = reactive<Record<number, true>>({});
watch(
  () => activeTabRef.value,
  (val) => {
    if (tabsList.value.find((tab) => tab.id === val && tab.needCache)) {
      tabsCache[val!] = true;
    }
  },
  { immediate: true, flush: "post" },
);

const onSelectReferenceMsg = (msg?: Msg, showAI?: boolean) => {
  selectReferenceMsg(msg, () => emitter.emit("AIScript", msg!));
  if (msg && showAI) {
    activeTabRef.value = tabsList.value[0].id;
  }
};

const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);

const handleKeyShortCut = (event: KeyboardEvent) => {
  // 检查是否按下了ctrl键和d键
  // 检测是否按下了'd'键，不区分大小写
  if (event.key.toLowerCase() === "d") {
    // 检测操作系统

    // 根据操作系统判断是否按下了对应的快捷键
    if (isMac && event.metaKey) {
      // Mac系统下的Command + D
      console.log("Mac系统下的Command + D");
    } else if (!isMac && event.ctrlKey) {
      // Windows系统下的Ctrl + D
      console.log("Windows系统下的Ctrl + D");
    } else {
      return;
    }

    // 阻止默认行为，比如浏览器默认的保存为书签的行为
    event.preventDefault();
    // 执行你的逻辑
    emit("removeCurrent");
  }
};
</script>

<style lang="less" scoped>
.activeTab {
  position: relative;

  &:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 35px;
    height: 3px;
    background: #04a5ff;
    border-radius: 2px;
  }
}
</style>
