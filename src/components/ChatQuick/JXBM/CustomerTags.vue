<template>
  <!-- 客户标签按钮 -->
  <span class="text-[#04a5ff] cursor-pointer" @click="showUserTags">查看</span>

  <!-- 客户标签弹窗 -->
  <Model v-model="showTagsModal" bg="rgba(0,0,0,.3)">
    <div
      class="relative bg-white overflow-hidden"
      :class="{
        'w-[600px] rounded-lg': !terminal,
        'w-[90vw] rounded-[5px]': terminal,
      }"
      style="line-height: 1.4"
    >
      <!-- Header with title and close button -->
      <div
        class="flex justify-between items-center border-b border-gray-200"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <div
          class="font-bold"
          :class="{
            'text-[18px]': !terminal,
            'text-[.36rem]': terminal,
          }"
        >
          客户标签
        </div>
        <div
          class="cursor-pointer absolute"
          :class="{
            'right-[20px] top-[20px]': !terminal,
            'right-[.3rem] top-[.3rem]': terminal,
          }"
          @click="showTagsModal = false"
        >
          <div
            :class="{
              'w-[24px] h-[24px]': !terminal,
              'w-[.48rem] h-[.48rem]': terminal,
            }"
            v-html="$icon_close"
          ></div>
        </div>
      </div>

      <!-- Content -->
      <div
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <div v-if="loadingTags" class="text-center py-20">加载中...</div>
        <div v-else>
          <!-- 已选标签 -->
          <div class="mb-16">
            <div class="mb-8 font-medium">已选标签：</div>
            <div v-if="selectedTags.length === 0" class="text-gray-500">
              暂无选中标签
            </div>
            <div v-else class="flex flex-wrap gap-8">
              <div
                v-for="(tag, index) in selectedTags"
                :key="index"
                class="px-8 py-6 rounded-8 bg-[#e6f2fb] text-14 text-[#04a5ff] shadow-sm flex items-center"
              >
                <span>{{ tag.name }}</span>
                <span
                  class="ml-4 cursor-pointer text-gray-500 hover:text-red-500"
                  @click="removeSelectedTag(tag)"
                  >×</span
                >
              </div>
            </div>
          </div>

          <!-- 标签选择 -->
          <div>
            <div class="mb-8 font-medium">选择标签：</div>
            <div class="flex items-center mb-16">
              <input
                v-model="tagSearchKeyword"
                type="text"
                :class="{
                  'w-full p-8 border border-[#e6eef4] rounded-8': !terminal,
                  'w-full p-[.16rem] border border-[#e6eef4] rounded-[.16rem]':
                    terminal,
                }"
                placeholder="搜索标签或输入新标签"
              />
            </div>

            <!-- 标签列表 -->
            <div class="flex flex-wrap gap-8 max-h-[200px] overflow-y-auto">
              <!-- 新标签选项 -->
              <div
                v-if="showNewTagOption"
                class="px-8 py-6 rounded-8 bg-green-100 text-14 text-green-700 cursor-pointer hover:bg-green-200 flex items-center"
                @click="addNewTag"
              >
                <span class="mr-2">+</span>
                <span>创建新标签: {{ tagSearchKeyword }}</span>
              </div>
              <div
                v-for="tag in filteredTagOptions"
                :key="tag.code"
                class="px-8 py-6 rounded-8 bg-gray-100 text-14 text-gray-700 cursor-pointer hover:bg-gray-200"
                @click="addSelectedTag(tag)"
              >
                {{ tag.name }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer with buttons -->
      <div
        class="flex justify-end border-t border-gray-200 space-x-[10px]"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <button
          class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"
          :class="{
            'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
            'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
          }"
          @click="showTagsModal = false"
        >
          取消
        </button>
        <button
          class="bg-[#04a5ff] text-white hover:bg-blue-600"
          :class="{
            'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
            'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
          }"
          :disabled="savingTags"
          @click="saveUserTags"
        >
          <span v-if="savingTags">保存中...</span>
          <span v-else>保存</span>
        </button>
      </div>
    </div>
  </Model>
</template>

<script lang="ts" setup>
import { inject, ref, computed, defineProps } from "vue";
import {
  TagValueVO,
  TagVO,
  getUserTags,
  getTagList,
  setUserTags,
  createTag,
} from "@/api/jxbm";
import { makeToast } from "@/utils/dom";
import Model from "@/components/Model.vue";
import $icon_close from "@/assets/close.svg?raw";

const props = defineProps<{
  userNo: string;
}>();

const terminal = inject("terminal") as 0 | 1;

// 客户标签相关
const showTagsModal = ref(false);
const loadingTags = ref(false);
const userTags = ref<TagValueVO[]>([]);
const tagOptions = ref<TagVO[]>([]);
const selectedTags = ref<TagVO[]>([]);
const tagSearchKeyword = ref("");
const loadingTagOptions = ref(false);
const savingTags = ref(false);

/**
 * 显示用户标签
 */
const showUserTags = async () => {
  if (!props.userNo) {
    makeToast("未获取到用户编号");
    return;
  }

  showTagsModal.value = true;
  loadingTags.value = true;
  userTags.value = [];
  selectedTags.value = [];
  tagOptions.value = [];
  tagSearchKeyword.value = "";

  try {
    // 获取用户已有标签
    const userTagsResponse = await getUserTags({
      bizType: "user",
      bizCode: props.userNo,
    });

    if (userTagsResponse.tags) {
      userTags.value = userTagsResponse.tags;
    }

    // 获取所有可用标签
    const tagListResponse = await getTagList({
      limit: 100,
    });

    if (tagListResponse) {
      tagOptions.value = tagListResponse;

      // 设置已选标签
      if (userTags.value.length > 0) {
        userTags.value.forEach((userTag) => {
          const matchedTag = tagOptions.value.find(
            (tag) => tag.code === userTag.tagCode,
          );
          if (matchedTag) {
            selectedTags.value.push(matchedTag);
          }
        });
      }
    }
  } catch (error) {
    console.error("获取标签数据失败", error);
    makeToast("获取标签数据失败");
  } finally {
    loadingTags.value = false;
  }
};

/**
 * 过滤标签选项
 */
const filteredTagOptions = computed(() => {
  if (!tagSearchKeyword.value) {
    return tagOptions.value.filter(
      (tag) =>
        !selectedTags.value.some(
          (selectedTag) => selectedTag.code === tag.code,
        ),
    );
  }

  return tagOptions.value.filter(
    (tag) =>
      tag.name.includes(tagSearchKeyword.value) &&
      !selectedTags.value.some((selectedTag) => selectedTag.code === tag.code),
  );
});

/**
 * 是否显示新建标签选项
 */
const showNewTagOption = computed(() => {
  if (!tagSearchKeyword.value) return false;

  // 检查是否已有相同名称的标签
  const existingTag = tagOptions.value.find(
    (tag) => tag.name === tagSearchKeyword.value,
  );
  return !existingTag;
});

/**
 * 添加选中标签
 */
const addSelectedTag = (tag: TagVO) => {
  if (
    !selectedTags.value.some((selectedTag) => selectedTag.code === tag.code)
  ) {
    selectedTags.value.push(tag);
  }
};

/**
 * 移除选中标签
 */
const removeSelectedTag = (tag: TagVO) => {
  const index = selectedTags.value.findIndex(
    (selectedTag) => selectedTag.code === tag.code,
  );
  if (index !== -1) {
    selectedTags.value.splice(index, 1);
  }
};

/**
 * 添加新标签
 */
const addNewTag = async () => {
  if (!tagSearchKeyword.value.trim()) {
    makeToast("标签名称不能为空");
    return;
  }

  loadingTagOptions.value = true;

  try {
    // 调用创建标签的API
    const newTag = await createTag({
      name: tagSearchKeyword.value.trim(),
    });

    if (newTag) {
      // 将新标签添加到标签列表和已选标签中
      tagOptions.value.push(newTag);
      selectedTags.value.push(newTag);
      tagSearchKeyword.value = "";
      makeToast("创建标签成功");
    }
  } catch (error) {
    console.error("创建标签失败", error);
    makeToast("创建标签失败");
  } finally {
    loadingTagOptions.value = false;
  }
};

/**
 * 保存用户标签
 */
const saveUserTags = async () => {
  if (!props.userNo) {
    makeToast("未获取到用户ID");
    return;
  }

  savingTags.value = true;

  try {
    const tagCodes = selectedTags.value.map((tag) => tag.code).join(",");

    const response = await setUserTags({
      bizType: "user",
      bizCode: props.userNo,
      tagCodes,
    });

    if (response && response.value) {
      makeToast("保存标签成功");
      showTagsModal.value = false;
    } else {
      makeToast("保存标签失败");
    }
  } catch (error) {
    console.error("保存用户标签失败", error);
    makeToast("保存标签失败");
  } finally {
    savingTags.value = false;
  }
};
</script>
