<template>
  <div class="px-14 min-h-full flex flex-col bg-[#e6f2fb] rounded-12">
    <Dashboard v-model="dashboard" :session="session" />
    <!-- Tab切换区 -->
    <div class="sticky top-0 z-1 bg-[#e6f2fb] text-16">
      <div
        class="tab-bar flex bg-[#f6f9fb]/95 backdrop-blur-sm rounded-8 p-4 mb-12 mt-17 shadow-sm"
      >
        <div
          class="flex-1 text-center py-8 rounded-8 cursor-pointer"
          :class="
            activeTab === 'recommend'
              ? 'bg-[#04a5ff] text-white'
              : 'text-[#333]'
          "
          @click="activeTab = 'recommend'"
        >
          班型列表
        </div>
        <div
          class="flex-1 text-center py-8 rounded-8 cursor-pointer"
          :class="
            activeTab === 'jiaxiao' ? 'bg-[#04a5ff] text-white' : 'text-[#333]'
          "
          @click="activeTab = 'jiaxiao'"
        >
          训练场列表
        </div>
      </div>
    </div>
    <!-- 班型列表内容 -->
    <div v-if="activeTab === 'recommend'">
      <div
        v-if="recommendClassList.length"
        v-for="item in recommendClassList"
        :key="item.classCode"
        class="mb-20 bg-white rounded-8 p-16 break-all text-16"
      >
        <div class="font-bold text-[#04a5ff] mb-8">
          {{ item.className }}
        </div>
        <div class="text-14 text-[#333] mb-4">
          <span class="font-bold">班型权益：</span
          >{{
            item.rightsLabels && item.rightsLabels.length
              ? item.rightsLabels.join("、")
              : "-"
          }}
        </div>
        <div class="text-14 text-[#333] mb-4">
          <span class="font-bold">班型亮点：</span
          >{{
            item.highlights && item.highlights.length
              ? item.highlights.join("、")
              : "-"
          }}
        </div>
        <div class="text-14 text-[#333] mb-4">
          <span class="font-bold">价格范围：</span
          ><span class="text-[#ff4d4f]">{{ item.priceRange }}</span>
        </div>
        <div class="flex space-x-10">
          <div
            class="px-16 py-6 bg-[#04a5ff] text-[#fff] rounded-full cursor-pointer"
            @click="sendJXBMMessage({ classCode: item.classCode })"
          >
            推荐班型
          </div>
          <div
            class="px-16 py-6 bg-[#ff4d4f] text-white rounded-full cursor-pointer"
            v-if="item.teamTplCode"
            @click="onGroupPurchaseClick(item.teamTplCode)"
          >
            拼团优惠
          </div>
        </div>
      </div>
      <div v-else class="text-center text-14 mt-20 text-gray-500">暂无数据</div>
    </div>
    <!-- 训练场列表内容（原有内容） -->
    <div v-if="activeTab === 'jiaxiao'">
      <div class="my-12 flex space-x-10 text-14/31">
        <select
          v-model="jiaxiaoId"
          class="w-0 overflow-hidden whitespace-nowrap text-ellipsis flex-1 px-4 text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 transition-colors"
          title="选择驾校"
        >
          <option value="">选择驾校</option>
          <option :value="item.jiaxiaoId" v-for="item in jiaxiaoList">
            {{ item.jiaxiaoName }}
          </option>
        </select>
        <select
          v-model="classCode"
          class="w-0 overflow-hidden whitespace-nowrap text-ellipsis flex-1 px-4 text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400 transition-colors"
          title="选择班型"
        >
          <option value="">选择班型</option>
          <option :value="item.classCode" v-for="item in classCodeList">
            {{ item.className }}
          </option>
        </select>
        <div
          class="px-16 bg-[#04a5ff] text-white rounded-full cursor-pointer"
          @click="fetchJiaxiaoGroupGoodsList"
        >
          查询
        </div>
      </div>
      <div
        v-if="jiaxiaoGroupGoodsList.length"
        class="mb-20"
        v-for="(classItem, i) in jiaxiaoGroupGoodsList"
      >
        <div
          class="mb-10 text-[#04A5FF]"
          :class="{
            'text-14': !terminal,
            'text-[.29rem]': terminal,
          }"
        >
          驾校{{ numZh(i + 1) }}：{{ classItem.jiaxiaoName }}
        </div>
        <div
          class="relative bg-[#F8FCFF] rounded-5 p-14 mb-8"
          v-for="goods in classItem.goodsList"
          :key="goods.goodsCode"
        >
          <div class="relative">
            <div
              class="font-semibold w-124 flex-1 text-[#333333]"
              :class="{
                'text-15': !terminal,
                'text-[.32rem]': terminal,
              }"
            >
              {{ goods.trainingFieldName }}
            </div>
            <div class="absolute top-0 right-0 flex space-x-8">
              <div class="relative">
                <div
                  class="h-31 px-8 cursor-pointer bg-[#04a5ff] text-[#fff] text-14/31 rounded-5 flex items-center justify-center"
                  @click="onRecommendClick(goods.goodsCode)"
                >
                  推荐
                </div>
                <div
                  class="absolute top-31 right-0 w-106 bg-[#F3F9FD] shadow-[0_2px_8px_rgba(0,0,0,0.15)] text-14/31 text-[#7F7F7F] text-center cursor-pointer"
                  v-if="recommendGoodsCode === goods.goodsCode"
                >
                  <div
                    class="hover:bg-[#E7F4FD]"
                    @click="
                      sendJXBMMessage({
                        classCode: goods.classCode,
                        goodsCode: goods.goodsCode,
                      })
                    "
                  >
                    学车商品卡片
                  </div>
                  <div
                    class="hover:bg-[#E7F4FD]"
                    @click="onReservationClick(goods)"
                  >
                    预约表单
                  </div>
                </div>
              </div>
              <div
                class="h-31 px-8 cursor-pointer bg-[#ff4d4f] text-[#fff] text-14/31 rounded-5 flex items-center justify-center"
                @click="onGroupPurchaseClick(goods.teamTplCode)"
                v-if="goods.teamTplCode"
              >
                拼团优惠
              </div>
            </div>
          </div>

          <div
            class="mt-9 flex"
            :class="{
              'text-14': !terminal,
              'text-[.29rem]': terminal,
            }"
          >
            <div class="w-60 text-[#A0A0A0]">商品ID：</div>
            <div class="flex-1 text-[#565656]">{{ goods.goodsCode }}</div>
          </div>
          <div
            class="mt-9 flex"
            :class="{
              'text-14': !terminal,
              'text-[.29rem]': terminal,
            }"
          >
            <div class="w-60 text-[#A0A0A0]">驾校：</div>
            <div class="flex-1 text-[#565656]">{{ goods.jiaxiaoName }}</div>
          </div>
          <div
            class="mt-9 flex"
            :class="{
              'text-14': !terminal,
              'text-[.29rem]': terminal,
            }"
          >
            <div class="w-60 text-[#A0A0A0]">训练场：</div>
            <div class="flex-1 text-[#565656]">
              {{ goods.trainingFieldAddress }}
              <div
                class="opacity-80 text-[0.9em]"
                v-html="
                  [goods.distanceText, goods.trainingFieldAreaName]
                    .filter(Boolean)
                    .join('&nbsp;&nbsp;')
                "
              ></div>
            </div>
          </div>
          <div
            class="mt-9 flex"
            :class="{
              'text-14': !terminal,
              'text-[.29rem]': terminal,
            }"
          >
            <div class="w-60 text-[#A0A0A0]">班型：</div>
            <div class="flex-1 text-[#565656]">{{ goods.className }}</div>
          </div>
          <div
            class="mt-9 flex"
            :class="{
              'text-14': !terminal,
              'text-[.29rem]': terminal,
            }"
          >
            <div class="w-60 text-[#A0A0A0]">价格：</div>
            <div class="flex-1 text-[#565656]">{{ goods.price }}元</div>
          </div>
        </div>
      </div>
      <div class="text-center text-14 mt-20 text-gray-500" v-else>暂无数据</div>
    </div>
    <!-- 训练场列表内容结束 -->
    <!-- 拼团优惠弹窗 -->
    <GroupPurchaseDialog
      v-if="showGroupDialog"
      :template-code="currentTeamTplCode"
      :session-key="session?.sessionKey || ''"
      @close="showGroupDialog = false"
      @success="onGroupSendSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { inject, ref, watch } from "vue";
import { storeToRefs } from "pinia";

import {
  MonaClassCode,
  MonaClassGoods,
  MonaDashboard,
  MonoJiaxiao,
  queryClassCodes,
  queryJiaxiaoList,
  sendRecommendClassMsg,
  sendVisitJiaXiaoMsg,
  MonaJiaxiaoGoods,
  queryJiaxiaoGroupGoodsList,
  MonaRecommendClass,
  queryRecommendClassList,
} from "@/api/jxbm";
import { numZh } from "@/utils/tools";
import { makeToast } from "@/utils/dom";
import { useSessionStore } from "@/store/index";
import GroupPurchaseDialog from "./JXBM/GroupPurchaseDialog.vue";
import Dashboard from "./JXBM/Dashboard.vue";

const { session } = storeToRefs(useSessionStore());
const jiaxiaoId = ref<any>("");
const classCode = ref("");
const jiaxiaoList = ref<MonoJiaxiao[]>([]);
const classCodeList = ref<MonaClassCode[]>([]);
const dashboard = ref<MonaDashboard>();
const activeTab = ref<"jiaxiao" | "recommend">("recommend");
const jiaxiaoGroupGoodsList = ref<MonaJiaxiaoGoods[]>([]);
const recommendClassList = ref<MonaRecommendClass[]>([]);
const recommendGoodsCode = ref<string>("");
const recommendLoaded = ref(false);
const jiaxiaoLoaded = ref(false);
const showGroupDialog = ref(false);
const currentTeamTplCode = ref("");

const fetchData = async () => {
  if (activeTab.value === "jiaxiao") {
    if (!jiaxiaoLoaded.value) {
      queryJiaxiaoList({ sessionKey: session.value!.sessionKey }).then(
        (res) => {
          jiaxiaoList.value = res.itemList;
        },
      );
      queryClassCodes({ sessionKey: session.value!.sessionKey }).then((res) => {
        classCodeList.value = res.itemList;
      });
      fetchJiaxiaoGroupGoodsList();
      jiaxiaoLoaded.value = true;
    }
  } else {
    if (!recommendLoaded.value) {
      fetchRecommendClassList();
      recommendLoaded.value = true;
    }
  }
};

const fetchJiaxiaoGroupGoodsList = async () => {
  const res = await queryJiaxiaoGroupGoodsList({
    sessionKey: session.value!.sessionKey,
    jiaxiaoId: jiaxiaoId.value,
    classCode: classCode.value,
  });
  jiaxiaoGroupGoodsList.value = res.itemList;
};

const fetchRecommendClassList = async () => {
  const res = await queryRecommendClassList({
    sessionKey: session.value!.sessionKey,
  });
  recommendClassList.value = res.itemList;
};

watch(
  () => session.value,
  () => {
    recommendLoaded.value = false;
    jiaxiaoLoaded.value = false;
    jiaxiaoId.value = "";
    classCode.value = "";
    recommendGoodsCode.value = "";
    jiaxiaoGroupGoodsList.value = [];
    recommendClassList.value = [];
    fetchData();
  },
  { immediate: true },
);

watch(activeTab, fetchData);

const sendJXBMMessage = async (goods: {
  classCode: string;
  goodsCode?: string;
}) => {
  await sendRecommendClassMsg({
    sessionKey: session.value!.sessionKey,
    ...goods,
  });
};

const terminal = inject("terminal") as 0 | 1;

/**
 * 点击推荐
 */
const onRecommendClick = (goodsCode: string) => {
  if (recommendGoodsCode.value === goodsCode) {
    recommendGoodsCode.value = "";
  } else {
    recommendGoodsCode.value = goodsCode;
  }
};

/**
 * 点击预约表单
 */
const onReservationClick = async (goods: MonaClassGoods) => {
  const { value } = await sendVisitJiaXiaoMsg({
    sessionKey: session.value!.sessionKey,
    userId: dashboard.value!.mucangId,
    classCode: goods.classCode,
    goodsCode: goods.goodsCode,
  });
  if (value) {
    makeToast("发送成功");
  } else {
    makeToast("发送失败");
  }
};

const onGroupPurchaseClick = (teamTplCode: string) => {
  currentTeamTplCode.value = teamTplCode; // 或 teamTplCode
  showGroupDialog.value = true;
};

const onGroupSendSuccess = () => {
  showGroupDialog.value = false;
  makeToast("发送成功");
};
</script>
