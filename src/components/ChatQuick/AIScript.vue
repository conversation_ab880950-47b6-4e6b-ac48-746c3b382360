<template>
  <div class="h-full flex flex-col" v-if="msg">
    <div class="bg-[#e6f2fb] rounded-12 pt-14 relative">
      <div class="flex items-center">
        <i class="w-3 h-14 bg-[#04a5ff] rounded-r-2 mr-10"></i>
        <span class="text-14/20 font-bold text-[rgba(0,0,0,0.85)]"
          >命中分层</span
        >
      </div>
      <div
        class="text-12 text-[#04a5ff] mr-20 text-center h-20 w-68 leading-20 rounded-5 cursor-pointer absolute top-14 right-40 flex items-center border-1 border-solid border-[#04a5ff] justify-center"
        @click="onInstructionClick"
        v-if="!terminal"
      >
        <img src="@/assets/ic_hsk.png" alt="" class="w-15 h-14 mr-2" />
        <span>话术库</span>
      </div>
      <div
        @click="fetchData"
        class="absolute top-14 right-14 border border-[#04a5ff] w-32 h-20 rounded-5 flex items-center justify-center cursor-pointer"
      >
        <i class="w-12 h-12 bg-cover bg-[url(./assets/right_refresh.png)]"></i>
      </div>
      <div
        class="pt-4 text-36/50 h-50 box-content font-semibold text-[#6e6e6e] text-center"
      >
        {{ categoryLeads?.category }}
      </div>
      <div
        class="pt-5 pb-25 text-12/18 h-18 box-content text-[#747474] text-center"
      >
        {{ categoryLeads?.description }}
      </div>
    </div>
    <div class="bg-[#e6f2fb] rounded-12 pt-14 mt-12 flex-1 h-0 flex flex-col">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <i class="w-3 h-14 bg-[#04a5ff] rounded-r-2 mr-10"></i>
          <span class="text-14/20 font-bold text-[rgba(0,0,0,0.85)]"
            >AI推荐回复内容</span
          >
        </div>
      </div>
      <div class="mx-14 mt-16 flex">
        <img
          class="w-28 h-28 rounded-full shrink-0"
          :src="msg?.customer!.avatar"
        />
        <div
          class="ml-5 mr-20 whitespace-nowrap overflow-hidden text-ellipsis bg-white rounded-12 rounded-tl-none px-14 py-10 text-14/20 text-[#565656]"
        >
          {{ (msg?.msgContent as any).text }}
        </div>
      </div>
      <div class="mx-14 mt-14 flex justify-end flex-1 h-0">
        <div
          class="mr-5 flex flex-col items-end h-full overflow-y-auto"
          v-if="generalInstructionRes"
        >
          <div
            class="mb-14 bg-[#04a5ff] px-14 py-10 rounded-12 rounded-tr-none text-white text-14/20"
            v-for="msg in generalInstructionRes.chatCommands"
          >
            {{ msg.commandDesc }}
          </div>
        </div>
        <div class="mr-14 text-14/28 text-red-500" v-else-if="error">
          {{ error }}
        </div>
        <div class="mr-14 text-14/28 text-[#565656]" v-else>AI思考中...</div>
        <img class="w-28 h-28 rounded-full shrink-0" :src="account!.avatar" />
      </div>
      <div class="px-14 py-10">
        <div
          class="btn"
          :class="{ disabled: !generalInstructionRes || sending }"
          @click="sendAIScript"
        >
          {{ sending ? "发送中..." : "一键发送" }}
        </div>
      </div>
    </div>
  </div>
  <div class="h-full" v-else>
    <WordLibraryComp
      ref="wordLibraryRef"
      :sessionkey="session!.sessionKey"
      :msgCode="session!.latestMsg?.msgCode"
    ></WordLibraryComp>
  </div>
</template>

<script lang="ts" setup>
import { Msg } from "@/api/chat";
import {
  GeneralInstructionRes,
  execInstruction,
  generalInstruction,
  getCategoryLeads,
  CategoryLeads,
} from "@/api/parrot";
import { makeToast } from "@/utils/dom";
import { EventEmitter, useEventEmitter } from "@/utils/eventEmitter";
import WordLibraryComp from "./WordLibrary.vue";
import { inject, ref, watch } from "vue";
import { useAccountStore, useSessionStore, useParamStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";

const { account } = storeToRefs(useAccountStore());
const { session } = storeToRefs(useSessionStore());
const { param } = storeToRefs(useParamStore());
const router = useRouter();
const msg = ref<Msg>();
const error = ref("");
const sending = ref(false);
const generalInstructionRes = ref<GeneralInstructionRes>();
const categoryLeads = ref<CategoryLeads>();
const wordLibraryRef = ref();
const terminal = inject("terminal");
const emitter = inject<EventEmitter>("emitter")!;

if (terminal) {
  onMessageSelect(param.value);
} else {
  useEventEmitter(emitter, "AIScript", onMessageSelect);
  // emitter.on("AIScript", onMessageSelect);
}

async function onMessageSelect(refMsg: Msg) {
  if (refMsg.msgType !== 11 && refMsg.msgType !== 10006) {
    return;
  }
  reset();
  msg.value = refMsg;
  await fetchData();
}

async function fetchData() {
  getCategoryLeads({
    mucangId: msg.value!.customer!.mucangId,
    tutorKemu: 1,
  }).then((res) => {
    categoryLeads.value = res;
  });
  generalInstructionRes.value = await generalInstruction({
    sessionKey: session.value!.sessionKey,
    msgCode: msg.value!.msgCode,
  })
    .catch((err) => {
      error.value = "生成失败";
      throw err;
    })
    .then((res) => {
      if (!res.chatCommands.length) {
        error.value = "暂无推荐内容";
        throw "没有指令";
      }
      return res;
    });
}

function reset() {
  sending.value = false;
  msg.value = undefined;
  generalInstructionRes.value = undefined;
  error.value = "";
  categoryLeads.value = undefined;
}

watch(
  () => session.value!,
  () => {
    reset();
  },
);

async function sendAIScript() {
  sending.value = true;
  await execInstruction({
    sessionKey: session.value!.sessionKey,
    msgCode: msg.value!.msgCode,
    instructionKeys: generalInstructionRes.value!.instructionKeys.join(","),
  })
    .then(() => {
      makeToast("发送成功", undefined, 500);
      reset();
    })
    .finally(() => {
      sending.value = false;
      terminal === 1 && router.back();
    });
}
/**
 * 切换到话术库
 */
const onInstructionClick = () => {
  msg.value = undefined;
};
</script>

<style lang="less" scoped>
.btn {
  font-size: 16px;
  text-align: center;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  border-radius: 12px;
  cursor: pointer;
  background: linear-gradient(
    225deg,
    #dc55e8,
    #2770ff 29%,
    #24baff 71%,
    #12fcd0
  );

  &:before {
    content: "";
    width: 18px;
    height: 18px;
    background-image: url("@/assets/magic.svg");
    background-size: cover;
    margin-right: 10px;
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
}
</style>
