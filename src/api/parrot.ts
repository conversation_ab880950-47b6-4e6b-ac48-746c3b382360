import request from "@/utils/request";

export interface GeneralInstructionRes {
  sessionKey: "";
  msgCode: "";
  instructionKeys: string[];
  chatCommands: Array<{
    type: number;
    content: string;
    instructionKey: string;
    commandDesc: string;
  }>;
}

export interface SessionTags {
  tagName: string;
  tagValue: string;
}

export function generalInstruction(data: {
  sessionKey: string;
  msgCode: string;
}) {
  return request<GeneralInstructionRes>({
    hostName: "parrot",
    url: "api/admin/sale-chat/general-instruction.htm",
    data,
  });
}

export function execInstruction(data: {
  sessionKey: string;
  msgCode: string;
  instructionKeys: string;
}) {
  return request<{
    value: boolean;
  }>({
    hostName: "parrot",
    url: "api/admin/sale-chat/exec-instruction.htm",
    data,
  });
}

export interface CategoryLeads {
  category: string;
  description: string;
}

export function getCategoryLeads(data: {
  mucangId: string;
  tutorKemu: 1 | 4;
  carType?: string;
}) {
  return request<CategoryLeads>({
    hostName: "parrot",
    url: "api/admin/customer/categorize-leads.htm",
    data,
  });
}

/**
 * 获取会话标签
 * @param {string} sessionKey 会话key
 */
export function getSessionTags(sessionKey: string) {
  return request<{
    itemList: SessionTags[];
  }>({
    hostName: "parrot",
    url: "/api/admin/sale-chat/get-session-tags.htm",
    data: {
      sessionKey,
    },
  });
}
