import request from "@/utils/request";

/**
 * 意向表单数据接口
 */
export interface IntentionForm {
  /** 主键 */
  id: number;
  /** 用户id */
  userId: string;
  /** 用户昵称 */
  nickName: string;
  /** 提交时间 */
  submitTime: number;
  /** 题库 */
  tiku: string;
  /** 答案 */
  answers: string;
  /** 跟进人姓名 */
  followUserName: string;
  /** 基础参数 */
  userClientInfo: string;
  /** 创建时间 */
  createTime: number;
  /** 创建人id */
  createUserId: number;
  /** 创建人名称 */
  createUserName: string;
  /** 更新时间 */
  updateTime: number;
  /** 更新人id */
  updateUserId: number;
  /** 更新人名称 */
  updateUserName: string;
  version: number;
}

export interface IntentionFormAnswers {
  /** 答案 */
  option: string;
  /** 题目 */
  shortName: string;
}

export interface MonaDashboard {
  mucangId: string;
  nickName: string;
  carType: string;
  cityName: string;
  phoneMask: string;
  longitude: number;
  latitude: number;
  /** 跟进人 */
  followUserName: string;
  /** 城市编码 */
  cityCode: string;
  /** 用户编号，用于查询标签和备注 */
  userNo?: string;
  /** 姓名 */
  name?: string;
  /** 性别 */
  gender?: number;
  /** 职业 */
  work?: string;
  /** 年龄 */
  age?: number;
  /** 模考次数 */
  mockTimes?: number;
  /** 近五次模考平均分 */
  latestFiveTimeAvgScore?: number;
  /** 总做题数 */
  totalExercisesCount?: number;
  /** VIP类型列表 */
  vipTypes?: string;
}

export interface MonaJiaxiaoGoods {
  jiaxiaoId: number;
  jiaxiaoName: string;
  goodsList: MonaClassGoods[];
}

export interface MonaRecommendClass {
  classCode: string;
  className: string;
  highlights: string[];
  priceRange: string;
  rightsLabels: string[];
  teamTplCode: string;
}

export interface MonaClassGoods {
  teamTplCode: string;
  classCode: string;
  className: string;
  goodsCode: string;
  jiaxiaoId: number;
  jiaxiaoName: string;
  trainingFieldName: string;
  trainingFieldAddress: string;
  distanceText: string;
  price: string;
  trainingFieldAreaName: string;
  detailReferer: string;
  trainingFieldId: number;
  longitude: number;
  latitude: number;
}

export interface MonoJiaxiao {
  jiaxiaoId: number;
  jiaxiaoName: string;
}

export interface MonaClassCode {
  classCode: string;
  className: string;
}

export interface MonaClassItemList {
  /** 训练场id */
  trainingFieldId: number;
  /** 训练场名称	 */
  trainingFieldName: string;
  /** 驾校id */
  jiaxiaoId: number;
  /** 驾校名称 */
  jiaxiaoName: string;
  /** 训练场地址 */
  trainingFieldAddress: string;
  /** 训练场区域名称 */
  trainingFieldAreaName: string;
  /** 距离 */
  distanceText: string;
  /** 经度 */
  longitude: number;
  /** 纬度 */
  latitude: number;
  /** 页面来源 */
  detailReferer: string;
  /** 班型列表 */
  classList: ClassList[];
}

export interface ClassList {
  /** 班级编码 */
  classCode: string;
  /** 班级名称 */
  className: string;
  /** 商品编码 */
  goodsCode: string;
  /** 价格 */
  price: string;
}

export interface MonaTeam {
  /** 过期时间 */
  expireTime: number;
  /** 发起时间 */
  launchTime: number;
  /** 发起人ID */
  launchUserId: string;
  /** 发起人昵称 */
  launchUserNickName: string;
  /** 发起人类型 */
  launchUserType: number;
  /** 已有名额 */
  joinedCount: number;
  /** 团编码 */
  teamCode: string;
  /** 团规模 */
  teamSize: number;
  /** 团状态 */
  teamStatus: number;
  /** 模板编码 */
  templateCode: string;
}

export function queryDashboard(data: {
  /** 会话 */
  sessionKey: string;
}) {
  return request<MonaDashboard>({
    hostName: "mona",
    url: "api/admin/im/query-dashboard.htm",
    data,
  });
}

export function queryJiaxiaoGroupGoodsList(data: {
  /** 会话 */
  sessionKey: string;
  /**驾校id  */
  jiaxiaoId?: number;
  /** 班型编码 */
  classCode?: string;
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
}) {
  return request<{
    itemList: MonaJiaxiaoGoods[];
  }>({
    hostName: "mona",
    url: "api/admin/im/query-jia-xiao-group-goods-list.htm",
    data,
  });
}

export function queryRecommendClassList(data: { sessionKey: string }) {
  return request<{
    itemList: MonaRecommendClass[];
  }>({
    hostName: "mona",
    url: "api/admin/im/query-recommend-class-list.htm",
    data,
  });
}

export function sendRecommendClassMsg(data: {
  sessionKey: string;
  classCode: string;
  goodsCode?: string;
}) {
  return request<{
    value: boolean;
  }>({
    hostName: "mona",
    url: "api/admin/im/send-recommend-class-msg.htm",
    data,
  });
}

export function queryUserPhone(data: { mucangId: string }) {
  return request<{
    value: string;
  }>({
    hostName: "mona",
    url: "api/admin/im/query-user-phone.htm",
    data,
  });
}

export function queryJiaxiaoList(data: { sessionKey: string }) {
  return request<{
    itemList: MonoJiaxiao[];
  }>({
    hostName: "mona",
    url: "api/admin/im/query-jiaxiao-list.htm",
    data,
  });
}

export function queryClassCodes(data: { sessionKey: string }) {
  return request<{
    itemList: MonaClassCode[];
  }>({
    hostName: "mona",
    url: "api/admin/im/query-class-codes.htm",
    data,
  });
}

/**
 * 查询同一个训练场下的班型列表
 * @param data
 * @returns
 */
export function queryClassItemList(data: {
  /** 训练场id */
  trainingFieldIds: string;
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
}) {
  return request<MonaClassItemList>({
    hostName: "mona",
    url: "api/admin/im/query-class-item-list.htm",
    data,
  });
}

/**
 * 销售跟进记录表-绑定
 */
export function salesFollowRecordBind(data: {
  /** 用户木仓id */
  userId: string;
}) {
  return request<{
    value: boolean;
  }>({
    hostName: "mona",
    url: "api/admin/sales-follow-record/bind.htm",
    data,
  });
}

/**
 * 查询隐私号码
 * @param params 请求参数
 * @returns Promise
 */
export function queryPrivacyPhone(params: {
  /** 会话密钥 */
  sessionKey: string;
  /** 用户ID */
  userId: string;
  /** 城市编码 */
  cityCode: string;
  /** 手机号 */
  phone: string;
}) {
  return request<{
    /** 隐私号码 */
    privacyPhone: string;
  }>({
    hostName: "mona",
    url: "/api/admin/im/query-privacy-phone.htm",
    method: "POST",
    data: params,
  });
}

/**
 * 获取用户意向表单列表
 * @param data 请求参数
 * @returns Promise
 */
export function getIntentionFormList(data: {
  /** 用户ID，即木仓ID */
  userId: string;
}) {
  return request<IntentionForm[]>({
    hostName: "mona",
    url: "/api/admin/questionnaire/list.htm",
    data,
  });
}

/**
 * 发预约看驾校的卡片消息
 */
export function sendVisitJiaXiaoMsg(data: {
  /** 会话key */
  sessionKey: string;
  /** 用户木仓id */
  userId: string;
  /** 班型编码 */
  classCode: string;
  /** 商品编码 */
  goodsCode: string;
}) {
  return request<{
    value: boolean;
  }>({
    hostName: "mona",
    url: "api/admin/im/send-visit-jiaxiao-appoint-msg.htm",
    data,
  });
}

/**
 * 获取用户备注
 * @param data 请求参数
 * @returns Promise
 */
export interface UserRemarkItem {
  id: number;
  userNo: string;
  remark: string;
  createTime: string;
  createUserId: number;
  createUserName: string;
  updateTime: string;
  updateUserId: number;
  updateUserName: string;
}

export function getUserRemarks(data: { userNo: string }) {
  return request<UserRemarkItem[]>({
    hostName: "mona",
    url: "api/admin/potential-user-remark/list.htm",
    data,
  });
}

/**
 * 新增用户备注
 * @param data 请求参数
 * @returns Promise
 */
export function createUserRemark(data: { userNo: string; remark: string }) {
  return request<boolean>({
    hostName: "mona",
    url: "api/admin/potential-user-remark/create.htm",
    data,
    method: "POST",
  });
}

/**
 * 获取用户标签
 * @param data 请求参数
 * @returns Promise
 */
export interface TagValueVO {
  tagName: string;
  tagCode: string;
  tagValue: any;
  tagValueType: number;
}

export interface TagRelListVO {
  tags: TagValueVO[];
}

export function getUserTags(data: { bizType: string; bizCode: string }) {
  return request<TagRelListVO>({
    hostName: "mona",
    url: "api/admin/tag-rel/list.htm",
    data,
  });
}

/**
 * 设置用户标签
 * @param data 请求参数
 * @returns Promise
 */
export function setUserTags(data: {
  bizType: string;
  bizCode: string;
  tagCodes: string;
}) {
  return request<{ value: boolean }>({
    hostName: "mona",
    url: "api/admin/tag-rel/set-tag.htm",
    data,
  });
}

/**
 * 获取标签列表
 * @param data 请求参数
 * @returns Promise
 */
export interface TagVO {
  id: number;
  code: string;
  categoryCode: string;
  name: string;
  valueType: number;
  desc: string;
  createTime: string;
  createUserId: number;
  createUserName: string;
  updateTime: string;
  updateUserId: number;
  updateUserName: string;
}

export function getTagList(data: {
  categoryCode?: string;
  name?: string;
  page?: number;
  limit?: number;
}) {
  return request<TagVO[]>({
    hostName: "mona",
    url: "api/admin/tag/list.htm",
    data,
  });
}

/**
 * 更新用户基础信息
 * @param data 请求参数
 * @returns Promise
 */
export function updateUserInfo(data: {
  sessionKey: string;
  name?: string;
  age?: number;
  work?: string;
  gender?: number;
}) {
  return request<{ value: boolean }>({
    hostName: "mona",
    url: "api/admin/im/modify-user-info.htm",
    data,
  });
}

/**
 * 创建标签
 * @param data 请求参数
 * @returns Promise
 */
export function createTag(data: { name: string }) {
  return request<TagVO>({
    hostName: "mona",
    url: "api/admin/tag/create.htm",
    data,
    method: "POST",
  });
}

/**
 * 查询进行中的拼团列表
 */
export function queryOnGoingTeamList(params: { templateCode: string }) {
  return request<{ itemList: MonaTeam[] }>({
    hostName: "mona",
    url: "api/admin/purchase-team/query-on-going-team-list.htm",
    data: params,
  });
}

/**
 * 发送拼团链接
 */
export function sendPurchaseTeamMsg(params: {
  sessionKey: string;
  type: 1 | 2;
  templateCode: string;
  teamCode?: string;
}) {
  return request<{ value: boolean }>({
    hostName: "mona",
    url: "api/admin/im/send-purchase-team-msg.htm",
    data: params,
    method: "POST",
  });
}
