/** 用户订单信息 */
import request from "@/utils/request";

export interface ImOrderList {
  id: number;
  /** 订单状态,1-待支付,2-取消支付,3-支付成功,4-已核销,6-已退款,7-退学退费,8-升级换班 */
  orderStatus: 1 | 2 | 3 | 4 | 6 | 7 | 8;
  /** 成交金额 */
  orderAmount?: number;
  /** 创建时间 */
  createTime?: number;
  /** 商品名称 */
  courseName?: string;
  /** 驾校名称 */
  jiaxiaoName?: string;
  /** 所属城市 */
  cityName?: string;
}

export const orderStatus: Record<ImOrderList["orderStatus"], string> = {
  1: "待支付",
  2: "取消支付",
  3: "支付成功",
  4: "已核销",
  6: "已退款",
  7: "退学退费",
  8: "升级换班",
};

export const getImOrderList = (mucangId: string) => {
  return request<{
    itemList: ImOrderList[];
  }>({
    url: "/api/admin/order/order/im-order-list.htm",
    method: "GET",
    hostName: "malibu",
    data: {
      userId: mucangId,
    },
  });
};
