import { FetchEventSource } from "@/utils/fetchEventSource";
import { MsgTypeMapper } from "@/utils/helpers";
import request, { HostNames } from "@/utils/request";
import { serverRenderApi, URLParams } from "@/utils/tools";

function parseMsg(msg: MsgAction) {
  if (msg.msgContent) {
    try {
      msg.msgContent = JSON.parse(msg.msgContent as any);
    } catch (e) {
      console.warn("消息格式有误");
    }
  }
}

function processSession(item: Session) {
  item.latestMsg && parseMsg(item.latestMsg);
  item.isGroup = item.sessionType === 10 && item.sessionSubType === 2;
  return item;
}

export enum StaffType {
  MC_EMPLOYEE = 1, // 木仓员工(客服)
}

export enum CustomerType {
  WeCom = 1, // 企业微信客服客户
}

export interface Pagination<T> {
  hasMore: boolean;
  cursor: string;
  itemList: T[];
  needClean: boolean;
}

export interface Session {
  avatar: string;
  newMsgCount: number;
  sessionKey: string;
  sessionName: string;
  updateTime: number;
  customerMucangId?: string;
  staffSsoId?: string;
  customerWxUnionId?: string;
  customerCode: string;
  latestMsg?: Msg;
  newMsg?: string;
  // 10 是im
  sessionType: number;
  // 1单聊，2群聊
  sessionSubType: number;
  /**
   * @internal
   * 是否是群聊
   */
  isGroup?: boolean;
  /**
   * @internal
   * 群聊是否已解散
   */
  groupMembers?: GroupMember[];
  /**
   * @internal
   * 群聊是否已解散
   */
  isDismissed?: boolean;
  /**
   * @internal
   * 是否置顶
   */
  isPinned?: boolean;
  // 群公告
  groupBulletin: string;

  /**
   * @internal
   * 是否需要更新已读状态
   */
  __markMsgRead?: boolean;
}

export type Msg = MsgAction & {
  sessionKey: string;
  account: Account;
  createTime: number;
  customer?: {
    avatar: string;
    id: number;
    nickname: string;
    type: CustomerType;
    code: string;
    mucangId: string;
    wxUnionId?: string;
  };
  id: string;
  msgTime: number;
  staff?: {
    avatar: string;
    id: number;
    nickname: string;
    type: StaffType;
    staffId: number;
  };
  msgCode: string;
  /** 消息是否已读 */
  readReceipts: boolean;
  /** 消息是否撤回 */
  isRevoke: boolean;
};

export interface Account {
  id: number;
  name: string;
  type: CustomerType; //账户类型.1=企微客服
  avatar: string;
}

export interface QAItem {
  category: string;
  question: string;
  answer: string;
}

//RefMsg 一样
export interface ReplyMsg {
  data: {
    type: MsgTypeMapper;
    content: _MsgAction["msgContent"];
  };
  // TODO: 木仓聊天室后台拿不到这个
  timMsgId?: string; // 腾讯原始消息id
  user: {
    // 要不要呢？怎么存，存im还是mucang的
    id?: string;
    nickName: "";
    avatar: "";
  };
}

export type MsgAction = _MsgAction & {
  labelList?: string[];
  msgContent: {
    replyMsg?: ReplyMsg;
    refMsg?: ReplyMsg;
  };
  name?: string;
};

export type QuickMsgActionType = {
  id: number;
  title: string;
} & MsgAction;

export type _MsgAction =
  | {
      /** 文本 */
      msgType: 11;
      msgContent: {
        text: string;
        botMsgKey?: string;
        // 后续所有消息都包含下面两个字段：
        atUsers?: AtUser[];
        isAtAll?: boolean;
        atAllFlag?: boolean;
      };
    }
  | {
      /** 系统提示 */
      msgType: 12;
      msgContent: {
        headText: string;
        list: Array<{
          type: "click";
          click: {
            text: string;
            id: string;
          };
        }>;
      };
    }
  | {
      /** 接收图片 */
      msgType: 13;
      msgContent: {
        name: string;
        encodedData: string;
      };
    }
  | {
      /** 接收音频 */
      msgType: 14;
      msgContent: {
        name: string;
        encodedData: string;
        duration: number;
      };
    }
  | {
      /** 接收视频 */
      msgType: 15;
      msgContent: {
        name: string;
        encodedData: string;
        snapshotEncodeData: string;
        duration: number;
      };
    }
  | {
      /** 接收文件 */
      msgType: 16;
      msgContent: {
        contentType: string;
        encodedData: string;
        previewUrl: string;
        name: string;
      };
    }
  | {
      /** 进入房间 */
      msgType: 91;
      msgContent: unknown;
    }
  | {
      /** 发送失败 */
      msgType: 92;
      msgContent: unknown;
    }
  | {
      /** 撤回 */
      msgType: 93;
      msgContent: unknown;
    }
  | {
      /** 通用事件(对客户隐藏) */
      msgType: 99;
      msgContent: {
        text: string;
        params: Record<string, string>;
        highlight: string;
      };
    }
  | {
      /** 图片 */
      msgType: 111;
      msgContent: {
        name: string;
        encodedData: string;
      };
    }
  | {
      /** 文件 */
      msgType: 112;
      msgContent: {
        name: string;
        encodedData: string;
      };
    }
  | {
      /** 视频 */
      msgType: 113;
      msgContent: {
        name: string;
        encodedData: string;
      };
    }
  | {
      /** 小程序 */
      msgType: 114;
      msgContent: {
        appid: string;
        imageKey: string;
        pagePath: string;
        title: string;
      };
    }
  | {
      /** 图文消息 */
      msgType: 115;
      msgContent: {
        title: string;
        desc: string;
        url: string;
        encodedData: string;
      };
    }
  | {
      /** 发送个人名片 */
      msgType: 116;
      msgContent: {
        name: string;
      };
    }
  | {
      /** 发送个人群名片 */
      msgType: 117;
      msgContent: {
        name: string;
      };
    }
  | {
      /** 发送学情分析 */
      msgType: 119;
      msgContent: {
        title: string;
        appid: string;
        pagePath: string;
        encodedData: string;
      };
    }
  | {
      /** 报价单 */
      msgType: 10000;
      msgContent: {
        customerName: string;
        discount: number;
        kemu: number;
        lessonTitleList: string[];
        msgText: string;
        originalPrice: number;
        price: number;
        reduction: number;
        serviceItems: Array<{
          lessonNum: number;
          serviceItemCode: string;
          subTotal: number;
        }>;
        studyPlan: number;
        productName: string;
        webActionUrl: string;
      };
    }
  | {
      /** 快捷指令 */
      msgType: 10001;
      msgContent: {
        id: number;
        text: string;
      };
    }
  | {
      /** 小程序链接 */
      msgType: 10002;
      msgContent: {
        title: string;
        subTitle: string;
        icon: string;
        cover: string;
        url: string;
      };
    }
  | {
      /** 信息收集 */
      msgType: 10003;
      msgContent: {
        url: string;
        title: string;
      };
    }
  | {
      /** 学情分析 */
      msgType: 10004;
      msgContent: {
        title: string;
        analysisTime: number;
        examSummary: {
          examStandard: boolean;
          examAvgScore: number;
          examTimes: number;
        };
        tikuKnowledge: {
          allKnowledgeCount: number;
          masteredKnowledgeCount: number;
          masteredRate: number;
          masteredCategoryName: string;
          knowledgeWrongList: Array<{
            knowledgeName: string;
          }>;
        };
        actionUrl: string;
        webActionUrl: string;
      };
    }
  | {
      /** 支付成功 */
      msgType: 10005;
      msgContent: {
        orderNo: string;
        title: string;
      };
    }
  | {
      /** 表情包 */
      msgType: 10006;
      msgContent: {
        text: string;
      };
    }
  | {
      /** 位置 */
      msgType: 10007;
      msgContent: {
        name: string;
        address: string;
        longitude: number;
        latitude: number;
      };
    }
  | {
      /** 通用卡片 */
      msgType: 10008;
      msgContent: {
        text: string;
        btns: Array<{
          icon?: string;
          name: string;
          color: [string, string];
          actionUrl: string;
        }>;
      };
    }
  | {
      /** 优惠券卡片 */
      msgType: 10009;
      msgContent: {
        price: number;
        couponTitle: string;
        expireTime: number;
        couponCode: string;
        actionUrl: string;
      };
    }
  | {
      /** 表格卡片 */
      msgType: 10010;
      msgContent: {
        recommendText: string;
        subTableList: Array<{
          title: string;
          columnRatioList: number[];
          rowList: Array<{
            isTitle: boolean;
            columnDataList: string[];
          }>;
        }>;
      };
    }
  | {
      /** 链接卡片 */
      msgType: 10011;
      msgContent: {
        title: string;
        desc: string;
        url: string;
        encodedData: string;
      };
    }
  | {
      /** 驾校报名卡片 */
      msgType: 10012;
      msgContent: {
        classCode: string;
        className: string;
        goodsCode: string;
        jiaxiaoId: string;
        schoolName: string;
        trainingFieldName: string;
        trainingFieldAddress: string;
        trainingFieldAreaName: string;
        price: string;
        distanceText: string;
        detailReferer: string;
      };
    }
  | {
      /** 图文链接卡片2 (拼团链接卡片) */
      msgType: 10020;
      msgContent: {
        desc: string;
        encodedData: string;
        title: string;
        url: string;
      };
    }
  | {
      /** 驾校线上成交-拼团消息 */
      msgType: 10021;
      msgContent: {
        classCode: string;
        className: string;
        goodsCode: string;
        jiaxiaoId: string;
        trainingFieldName: string;
        trainingFieldLabel: string;
        trainingFieldAreaName: string;
        distanceText: string;
        price: string;
        imgUrl: string;
        introduction: string;
        brandInfoList: Array<{
          iconUrl: string;
          text: string;
        }>;
        labels: string[];
        teamTplDTO?: {
          teamSize: number;
          discount: string;
          teamPrice: string;
        };
      };
    }
  | {
      /** im群聊进入 */
      msgType: 200;
      msgContent: {
        EventTime: number;
        GroupId: string;
        JoinType: "Invited";
        NewMemberList: Array<{ Member_Account: string; nickName: string }>;
        Operator_Account: "administrator";
      };
    }
  | {
      /** im群聊退出 */
      msgType: 201;
      msgContent: {
        EventTime: number;
        ExitMemberList: Array<{ Member_Account: string; nickName: string }>;
        ExitType: "Kicked";
        GroupId: string;
        Operator_Account: "administrator";
      };
    }
  | {
      /** im群资料修改 */
      msgType: 202;
      msgContent: {
        atAllFlag: boolean;
        time: number;
        content: string;
        modifierUserId: string;
      };
    }
  | {
      /** im群解散 */
      msgType: 203;
      msgContent: {
        atAllFlag: boolean;
        time: number;
        content: string;
        modifierUserId: string;
      };
    };

export type SessionListType = 101 | 111;

export interface GroupMember {
  avatar: string;
  mucangId: string;
  nickName: string;
}

export interface AtUser {
  faceUrl: string;
  nickName: string;
  userId: string;
}

export interface Tabs {
  defaultTag: boolean;
  order: number;
  tagCode: string;
  tagName: string;
  unreadCount: number;
}

export interface SessionTab {
  invisibleTags: Tabs[];
  visibleTags: Tabs[];
}

export const getSessionList = serverRenderApi(async function (data: {
  sessionListType: SessionListType;
  tagCode: string;
  cursor?: string;
}) {
  const res = await request<Pagination<Session>>({
    url: "api/admin/msg/session-list.htm",
    data,
  });
  res.itemList.forEach(processSession);
  return res;
});

export function getSessionDetail(data: { sessionKey: string }) {
  return request<Session>({
    url: "api/admin/msg/session-info.htm",
    data,
  }).then((session) => processSession(session));
}

export async function getMsgList(data: {
  sessionKey: string;
  cursor?: string;
}) {
  const res = await request<Pagination<Msg>>({
    url: "api/admin/msg/msg-list.htm",
    data,
  });
  res.itemList.forEach(parseMsg);
  return res;
}

export function getAccountList(data: { sessionKey: string }) {
  return request<{
    itemList: Array<Account>;
  }>({
    url: "api/admin/msg/account-list.htm",
    data,
  });
}

export async function sendMsg(
  data: MsgAction & {
    sessionKey: string;
    accountId: number;
    referenceMsgId?: string;
  },
) {
  const res = await request<Msg>({
    method: "POST",
    url: "api/admin/msg/send-msg.htm",
    data: JSON.stringify({
      ...data,
      msgContent: data.msgContent
        ? JSON.stringify(data.msgContent)
        : data.msgContent,
    }) as any,
    // @ts-ignore
    ajaxOptions: {
      headers: {
        "Content-Type": "application/json",
      },
      contentType: false,
      processData: false,
    },
  });
  parseMsg(res);
  return res;
}

export function listenMsg(
  _data: {},
  onmessage: (msg: Msg) => void,
  ondisconnected: () => void,
) {
  return new Promise<void>((resolve, reject) => {
    // 创建 EventSource 实例，连接到服务器的事件流
    var eventSource = new FetchEventSource(
      HostNames.chat +
        "api/admin/msg/listen.htm" +
        `?bizCode=${URLParams.get("bizCode") || "10001"}`,
      {
        redirect: "follow",
        credentials: "include",
        headers: {
          "X-SSO-Token": localStorage.getItem("MCXTK_chat-platform")!,
        },
      },
    );

    console.log("sse连接中...");

    // 监听服务器推送的消息
    eventSource.addEventListener("Msg", function (event: any) {
      console.log("收到消息:", event.lastEventId, event.type, event.data);
      const msg: Msg = JSON.parse(event.data);
      parseMsg(msg);
      onmessage(msg);
    });

    // 监听连接打开事件
    eventSource.onopen = function () {
      console.log("连接已打开");
      resolve();
    };

    // 监听错误事件
    eventSource.onerror = function (err) {
      console.log("连接发生错误", err);
      // 如果需要，可以尝试重新连接
      eventSource.close();
      reject();
      ondisconnected();
    };
  });
}

export function markMsgRead(data: { sessionKey: string }) {
  return request<{
    value: boolean;
  }>({
    url: "api/admin/msg/mark-read.htm",
    method: "POST",
    data,
  });
}

export function markProcessed(data: {
  sessionListType: SessionListType;
  sessionKey: string;
}) {
  return request<{
    value: boolean;
  }>({
    url: "api/admin/msg/mark-processed.htm",
    method: "POST",
    data,
  });
}

export function markReplied(data: { sessionKey: string }) {
  return request<{
    value: boolean;
  }>({
    url: "api/admin/msg/mark-replied.htm",
    method: "POST",
    data,
  });
}

export async function getAISuggestions(data: { referenceMsgId: string }) {
  let res = await request<{
    recommendId: string;
    recommendContents: MsgAction[];
  }>({
    url: "api/admin/chat-assistant/general-recommend.htm",
    data,
  });
  if (!res) {
    res = {
      recommendId: "",
      recommendContents: [],
    };
  }
  res.recommendContents.forEach(parseMsg);
  return res;
}

export function getQASuggestions(data: {}) {
  return request<{
    itemList: QAItem[];
  }>({
    url: "api/admin/chat-assistant/get-quick-replies.htm",
    data,
  });
}

export async function getQuickInstructions(data: {}) {
  const res = await request<{
    itemList: MsgAction[];
  }>({
    url: "api/admin/chat-assistant/get-quick-instructions.htm",
    data,
  });
  res.itemList.forEach(parseMsg);
  return res;
}

export function revokeMsg(data: { msgId: string }) {
  return request({
    url: "api/admin/msg/msg-withdraw.htm",
    method: "POST",
    data,
  });
}

export function getGroupMembers(data: { sessionKey: string }) {
  return request<{
    itemList: GroupMember[];
  }>({
    url: "api/admin/chat-group/member-list.htm",
    data,
  });
}

export function kickOffGroupMember(data: {
  sessionKey: string;
  mucangId: string;
}) {
  return request<{
    value: boolean;
  }>({
    url: "api/admin/chat-group/delete-group-mucang-id.htm",
    data,
  });
}

export async function getSessionByMucangId(data: { mucangId: string }) {
  const res = await request<{ itemList: Session[] }>({
    url: "/api/admin/session/get-session-by-mucang-id.htm",
    data,
  });
  const session = res.itemList[0];
  return session ? processSession(session) : null;
}

export async function getOnlineStatus(data: { sessionKey: string }) {
  return request<{
    value: boolean;
  }>({
    url: "api/admin/im-customer/get-online-status.htm",
    data,
  });
}

export interface ListSessionItem {
  avatar: string;
  name: string;
  sessionKey: string;
}

export interface ListSessionRes {
  groupChatList: ListSessionItem[];
  singleChatList: ListSessionItem[];
}

export function listSessions(data: { name: string }) {
  return request<ListSessionRes>({
    url: "api/admin/session/list-session.htm",
    data,
  });
}

export async function getPinnedSessionList(data: {}) {
  const res = await request<{
    itemList: Session[];
  }>({
    url: "api/admin/session-staff/get-pinned-session-list.htm",
    data,
  });
  res.itemList.forEach(processSession);
  return res;
}

/** 更新置顶状态 */
export function updatePinnedStatus(data: {
  sessionKey: string;
  status: boolean;
  tagCode: string;
}) {
  return request<{
    value: boolean;
  }>({
    url: "api/admin/session-staff/update-pinned-status.htm",
    data,
  });
}

export function getNoDisturbSessionList(data: {}) {
  return request<{
    itemList: string[];
  }>({
    url: "api/admin/session-staff/get-no-disturb-session-list.htm",
    data,
  });
}

export function updateNoDisturbStatus(data: {
  sessionKey: string;
  status: boolean;
  tagCode: string;
}) {
  return request<{
    value: boolean;
  }>({
    url: "api/admin/session-staff/update-no-disturb-status.htm",
    data,
  });
}

/**
 * 获取会话标签
 * @returns
 */
export function getSessionTab() {
  return request<SessionTab>({
    url: "/api/admin/staff-tag/list-staff-tags.htm",
  });
}

/**
 * 更新会话标签
 */
export function updateSessionTab(data: string) {
  return request<{
    value: boolean;
  }>({
    url: "/api/admin/staff-tag/update-staff-tags.htm",
    method: "POST",
    data: {
      tagCodes: data,
    },
  });
}
